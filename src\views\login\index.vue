<template>
  <div class="login-wrapper">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-text">登录中...</div>
    </div>
    <div v-else-if="state.showRetryButton" class="retry-container">
      <div class="retry-text">登录失败，请重新尝试</div>
      <button class="retry-button" @click="retryLogin">重新登录</button>
    </div>
    <div v-else class="loading-container">
      <div class="loading-text">正在跳转到登录页...</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { SLMessage } from '@/utils/Message';
import { useBusinessStore, useUserStore } from '@/store';
import { redirectToSSO } from '@/utils/ssoConfig';

const state = reactive<{
  loading: boolean;
  showRetryButton: boolean;
  loginForm: {
    username: string;
    password: string;
  };
  qrcodes: { name: string; url: string }[];
  plugins: { name: string; url: string; img: string }[];
  soundOn: boolean;
}>({
  loading: false,
  showRetryButton: false,
  loginForm: {
    username: '',
    password: '',
  },
  qrcodes: [
    { name: 'App', url: '' },
    { name: '公众号', url: window.SITE_CONFIG.LOGIN_CONFIG.WXPUBLICACCOUNTURL },
  ],
  plugins: [
    {
      name: 'hik',
      img: 'hik.png',
      url: 'https://pic.hik-cloud.com/chain/download/cloudViewSetup.exe',
    },
    {
      name: 'chrome',
      img: 'chrome.png',
      url: 'https://www.google.com/intl/zh-CN/chrome/thank-you.html?statcb=1&installdataindex=empty&defaultbrowser=0',
    },
    {
      name: 'lodop',
      img: 'lodop.png',
      url: 'https://www.lodop.net/download.html',
    },
  ],
  soundOn: false,
});
const router = useRouter();

// 使用回传的 code 调用后端换取 token 的流程
const ssoProcessed = ref(false);
const getCodeFromUrl = () => {
  const searchParams = new URLSearchParams(window.location.search);
  let code = searchParams.get('code') || '';
  if (!code) {
    code = (router.currentRoute.value.query?.code as string) || '';
  }
  if (!code && window.location.hash) {
    const hash = window.location.hash;
    const idx = hash.indexOf('?');
    if (idx !== -1) {
      const hashQuery = new URLSearchParams(hash.substring(idx + 1));
      code = hashQuery.get('code') || '';
    }
  }
  return code;
};

const ssoLogin = async (code: string) => {
  if (!code) {
    return;
  }
  if (ssoProcessed.value) {
    return;
  }
  ssoProcessed.value = true;
  try {
    state.loading = true;
    // 使用统一的调用，Login 会把 { validateCode, redirect_uri } 发送给后端
    await useUserStore().Login({ code });

    const roles = useUserStore().user?.authority?.split(',') || [];
    let targetPath = '/home';
    if (useBusinessStore().usePortal) {
      if (roles[0] === 'SYS_ADMIN') {
        targetPath = '/';
      } else {
        targetPath = '/app';
      }
    }
    await router.push({ path: targetPath });
  } catch (error) {
    const errMsg = (error as Error)?.message || '登录失败';
    SLMessage.error(errMsg);
    console.error('SSO登录失败:', error);
    // 不自动重定向到 SSO，避免与用户中心产生循环；显示错误后等待用户重试
    ssoProcessed.value = false;
    state.showRetryButton = true;

    // 清理URL中的code参数，避免页面刷新时重复处理
    const url = new URL(window.location.href);
    url.searchParams.delete('code');
    url.searchParams.delete('state');
    window.history.replaceState({}, document.title, url.toString());
  } finally {
    state.loading = false;
  }
};

// 重试登录方法
const retryLogin = () => {
  state.showRetryButton = false;
  // 使用统一的SSO配置跳转到用户中心登录
  redirectToSSO();
};

const setAppUrl = () => {
  state.qrcodes[0].url = window.SITE_CONFIG.appDownloadUrl; //'http://***********:8999/group1/M00/00/03/wKgA7GWOiQmAQrcPBbB2Az4FKCE867.apk';
};
onMounted(() => {
  setAppUrl();
  // 如果回调地址带有 code（支持 search / router query / hash），则处理 SSO 登录流程
  const code = getCodeFromUrl();
  if (code) {
    ssoLogin(code);
  } else {
    // 没有code，延迟一下再跳转到SSO登录，避免页面闪烁
    setTimeout(() => {
      retryLogin();
    }, 1000);
  }
});
</script>
<style lang="scss" scoped>
.qrcode {
  display: flex;
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 10;

  .qrcode-item {
    display: inline-flex;
    margin-right: 20px;
    flex-direction: column;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 10px;
    border-radius: 8px;
    backdrop-filter: blur(1px);

    .qrcode-title {
      margin-bottom: 10px;
      color: #ffffff;
      font-size: 16px;
      text-align: center;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
  }
}

/* 新增声音开关样式 */
.sound-toggle {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 12;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);

  &:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.12);
  }

  svg {
    width: 20px;
    height: 20px;
    fill: currentColor; /* 确保图标跟随 color */
    stroke: currentColor;
  }
}

.plugins {
  position: absolute;
  left: 20px;
  bottom: 20px;
  display: flex;
  flex-direction: column;
  z-index: 10;
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(1px);

  .plugins-title {
    font-size: 14px;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .plugin-item {
    font-size: 14px;
    line-height: 2;
    margin-bottom: 5px;

    .plugin-logo {
      &.chrome {
        height: 18px;
        margin-left: 8px;
        margin-bottom: 8px;
        filter: brightness(1.2);
      }

      &.lodop {
        height: 16px;
        margin-left: 10px;
        filter: brightness(1.2);
      }

      &.hik {
        height: 30px;
        filter: brightness(1.2);
      }
    }
  }
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.login-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
  font-family: 'PingFang SC';
  font-style: normal;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
    z-index: 2;
  }
}

.loading-container,
.retry-container {
  position: relative;
  z-index: 10;
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
  min-width: 300px;
}

.loading-text,
.retry-text {
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  margin-bottom: 20px;
}

.retry-button {
  background: linear-gradient(135deg, #0080ff 0%, #0062c3 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 128, 255, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(0, 128, 255, 0.4);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(0, 128, 255, 0.3);
  }
}

.login-box {
  position: absolute;
  width: 420px;
  left: 58%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 60px 40px;
  z-index: 10;

  .login-box__header {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    margin-bottom: 40px;

    .logo {
      margin-right: 16px;
      height: 32px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    .title {
      color: #ffffff;
      font-weight: 600;
      font-size: 24px;
      line-height: 32px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
  }

  .login-box__main {
    width: 100%;
    margin-bottom: 30px;

    .el-form-item {
      margin-bottom: 25px;
    }
  }

  .login-box__footer {
    width: 100%;

    :deep(.el-button) {
      background: linear-gradient(135deg, #0080ff 0%, #0062c3 100%);
      border: none;
      height: 46px;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 1px;
      box-shadow: 0 4px 12px rgba(0, 128, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 18px rgba(0, 128, 255, 0.4);
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(0, 128, 255, 0.3);
      }
    }
  }
}

.login-tips {
  position: absolute;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  z-index: 10;
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
  text-align: center;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
}
</style>
<style lang="scss">
.login-box__main {
  .el-input {
    --el-border-color: rgba(255, 255, 255, 0.3);
    --el-input-bg-color: rgba(255, 255, 255, 0.1);

    .el-input__wrapper {
      background-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
      border-radius: 8px;

      &:hover,
      &:focus {
        box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.4);
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #0080ff;
      }
    }

    .el-input__inner {
      color: #ffffff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .el-input__prefix {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  input:-webkit-autofill,
  textarea:-webkit-autofill,
  select:-webkit-autofill {
    background-color: transparent !important;
    background-image: none;
    transition: background-color 50000s ease-in-out 0s;
    -webkit-text-fill-color: #ffffff !important;
  }

  input:-internal-autofill-previewed,
  input:-internal-autofill-selected {
    color: #ffffff !important;
    transition: background-color 50000s ease-in-out 0s !important;
  }

  .el-form-item__label {
    color: rgba(255, 255, 255, 0.9);
  }
}

.el-form-item__error {
  color: #ff6b6b;
}
</style>
