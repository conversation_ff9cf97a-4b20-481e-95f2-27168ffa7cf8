import vue from '@vitejs/plugin-vue';
import path from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import UnpluginComponents from 'unplugin-vue-components/vite';
import { defineConfig, loadEnv } from 'vite';
import { createHtmlPlugin } from 'vite-plugin-html';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import { viteExternalsPlugin } from 'vite-plugin-externals';
import resolveExternalsPlugin from 'vite-plugin-resolve-externals';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isPro = mode === 'production';
  return {
    plugins: [
      // VueDevTools(),
      vue(),
      viteExternalsPlugin({
        cesium: 'Cesium',
      }),

      createHtmlPlugin({
        minify: isPro,
        entry: '/src/main.ts',
        inject: {
          data: {
            ...env,
          },
        },
      }),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/, // .md
        ],
        eslintrc: {
          enabled: false,
        },
        dirs: ['./src/plugins/dayjs'],
        imports: [
          'vue',
          'pinia',
          'vue-router',
          {
            // vue: ['ComputedRef'],
            '@vueuse/core': [
              // named imports
              'useMouse', // import { useMouse } from '@vueuse/core',
              'useDark',
              'useToggle',
              'useBluetooth',
              // alias
              ['useFetch', 'useMyFetch'], // import { useFetch as useMyFetch } from '@vueuse/core',
            ],

            moment: [['default', 'moment']],
          },
        ],
      }),
      UnpluginComponents({
        dirs: ['src/components'],
        resolvers: [ElementPlusResolver()],
      }),

      // 在生产构建禁用图片优化以降低内存占用
      !isPro &&
        ViteImageOptimizer({
          logStats: false,
        }),
      resolveExternalsPlugin({
        cesium: 'cesium',
      }),
    ].filter(Boolean),
    resolve: {
      alias: [
        {
          find: '@',
          replacement: path.resolve(__dirname, 'src'),
        },
        {
          find: '~@',
          replacement: path.resolve(__dirname, 'src'),
        },
      ],
    },
    optimizeDeps: {
      include: ['@/../lib/vform/designer.umd.js', '@/../lib/vform/render.umd.js'],
    },
    server: {
      host: '0.0.0.0',
      port: 5000,
      hmr: true,
      proxy: {
        '/arcgis/rest/services/ANQING/': {
          target: 'http://***********:28090', // 本地
          changeOrigin: true,
          rewrite(path) {
            return path;
          },
        },
        '/authentication': {
          // target: 'http://127.0.0.1:8081',
          target: 'http://***********:8848/',
          changeOrigin: true,
        },
        '/api': {
          // target:'http://127.0.0.1:8081',
          target: 'http://***********:8848/',
          // target: 'http://*********:8202',
          // target: 'http://*********:8081',
          // ws: true, //代理websockets/
          changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
        },
        '/file': {
          target: 'http://***********:8848',
          changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
        },
        '/istar': {
          target: 'http://***********:8848',
          changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
        },
        '/geoserver': {
          target: 'http://10.0.31.216:18080',
          changeOrigin: true,
        },
        '/gisAnalyse': {
          target: 'http://10.6.7.242:8123',
          changeOrigin: true,
        },
        '/gisData': {
          target: 'http://10.6.7.242:8123',
          changeOrigin: true,
        },
        '/validDate': {
          target: 'http://10.6.7.242:8123',
          changeOrigin: true,
        },
        '/waterModels': {
          target: 'http://10.6.6.110:10011',
          changeOrigin: true,
        },
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          charset: false,
          api: 'modern-compiler',
        },
      },
    },
    // build: {
    //   commonjsOptions: {
    //     include: /node_modules|lib/
    //   },
    //   rollupOptions: {
    //     output: {
    //       chunkFileNames: 'static/js/[name]-[hash].js',
    //       entryFileNames: 'static/js/[name]-[hash].js',
    //       assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
    //       // 手动拆分超大依赖，降低单次内存峰值
    //       manualChunks: {
    //         echarts: ['echarts', 'echarts-gl', 'echarts-liquidfill', 'vue-echarts'],
    //         three: ['three', '@three-ts/orbit-controls'],
    //         xlsx: ['xlsx', 'exceljs'],
    //         // arcgis: ['@arcgis/core'],
    //         cesium: ['cesium'],
    //         d3: ['d3'],
    //         orillusion: ['@orillusion/core', '@orillusion/stats'],
    //         turf: ['@turf/turf'],
    //         editor: ['@wangeditor/editor', '@wangeditor/editor-for-vue'],
    //         codemirror: [
    //           'codemirror',
    //           '@codemirror/lang-javascript',
    //           '@codemirror/lang-json',
    //           '@codemirror/lang-sql',
    //           '@codemirror/theme-one-dark',
    //           'vue-codemirror'
    //         ],
    //         video: ['video.js', 'xgplayer', 'xgplayer-flv', 'xgplayer-hls', 'flv.js', 'hls.js'],
    //         maps_misc: ['@antv/l7', '@antv/l7-maps'],
    //         misc: ['axios', 'lodash-es', 'moment', 'dayjs']
    //       }
    //     }
    //   }
    // }
  };
});
