// 路由守卫废弃了next函数的用法，使用return的方式 allow guards to return a value instead of calling next
// 参考 https://github.com/vuejs/rfcs/blob/master/active-rfcs/0037-router-return-guards.md#motivation

import NProgress from 'nprogress'; // Progress 进度条
import { ElMessage } from 'element-plus';
import router from '@/router';
import {
  useAppStore,
  useBusinessStore,
  usePermissionStore,
  useTagsStore,
  useUserStore,
} from '@/store';
import 'nprogress/nprogress.css'; // Progress 进度条样式
// import { getToken } from '@/api/login' // 验权
import { getUserProject } from '@/api/user';
import { getCurTenant, getCurrentTenantList } from '@/api/tenant';
import { GetDeepestRoutePath, hasPermission, refreshAllRoutes } from '@/utils/RouterHelper';

let routerJumpCounter = 0;
NProgress.configure({ showSpinner: false }); // NProgress Configuration n进程配置
const whiteList = ['/login', '/resetPassword', '/requestResetPwd', '/jumping', '/jumpingV2']; // 不重定向白名单 /realTimeAlarm , '/eventAlarm/historyAlarm', '/scada', '/phonePage'
router.beforeEach(async to => {
  const userStore = useUserStore();
  const appStore = useAppStore();
  const businessStore = useBusinessStore();
  const permissionStore = usePermissionStore();
  NProgress.start();
  appStore.TOGGLE_menuShow(false);
  const token = userStore.token;
  // 在免登录白名单，直接进入
  if (whiteList.indexOf(to.path) !== -1) {
    NProgress.done();
  } else if (!token) {
    NProgress.done();
    // 没有 token，则重定向到用户中心登录页（携带回调到 /login）
    try {
      const ssoBase = 'http://10.0.31.213:3002/#/login';
      const clientId = 'huachi-water';
      const stateStr = 'ejDeIX';
      const redirectUri = encodeURIComponent(window.location.origin + '/login');
      const ssoUrl = `${ssoBase}?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&state=${stateStr}`;
      window.location.href = ssoUrl;
    } catch (e) {
      console.error('跳转到用户中心失败', e);
      // return { path: '/login', replace: true };
    }
    return false;
  } else {
    try {
      const { user } = userStore;
      if (!user) {
        // 没有用户信息则拿取用户信息
        await userStore.GetInfo();
        await Promise.all([userStore.GetBtnPerms(), userStore.InitTenantInfo()]);
      }
      // 设置全局项目树
      await businessStore.INIT_projectInfo();
      // if (businessStore.shouldRefreshSideBar) {
      //   await handleAppChange()
      //   return {
      //     path: to.path,
      //     query: to.query,
      //     meta: to.meta
      //   }
      // }
    } catch (error) {
      ElMessage.error('登录信息超时');
      NProgress.done();
      userStore.LogOut();
    }

    if (to.path === '/home') {
      appStore.SET_appid('');
      appStore.SET_appname('');
      await refreshAllRoutes();
      return true;
    }
    const permited = hasPermission(to.meta.roles);

    if (permited === true) {
      const roles = userStore.user?.authority?.split(',') || [];
      if (roles.indexOf('SYS_ADMIN') === -1) {
        if (to.path !== '/404withlayout') {
          if (!permissionStore.addRouters?.length) {
            // 不是管理员且没有从后台获取路由，则重新获取路由
            await refreshAllRoutes();
            // 处理没有获取到路由信息时跳转到404
            const addRouters = permissionStore.addRouters || [];
            // const path = GetDeepestRoutePath(addRouters[0])
            // 处理当原来路由，原来是'/'则跳转到异步路由的第一个最深层级的可访问路由
            const path = to.fullPath === '/' ? GetDeepestRoutePath(addRouters[0]) : to.fullPath;
            //
            if (!addRouters.length) {
              // 重复尝试最多1次
              if (routerJumpCounter >= 1) {
                routerJumpCounter = 0;
              } else {
                // 尝试次数+1并重新跳转一遍
                routerJumpCounter++;
                if (path) {
                  return { path };
                }
              }
            } else {
              routerJumpCounter = 0;
              if (path) {
                return { path };
              }
            }
          } else {
            routerJumpCounter = 0;
          }
        } else {
          routerJumpCounter = 0;
        }
      } else if (!permissionStore.routers.length) {
        // 是管理员但是没有处理过路由，则处理路由
        await refreshAllRoutes();
        const routers =
          permissionStore.routers?.filter(
            item => item.hidden !== true && item.meta?.hidden !== true,
          ) || [];
        const path = to.fullPath === '/' ? GetDeepestRoutePath(routers[0]) : to.fullPath;
        if (path) {
          return { path };
        }
      }
      // 处理企业列表
      if (roles.indexOf('TENANT_SUPPORT') !== -1 || roles.indexOf('TENANT_PROMOTE') !== -1) {
        if (!getCurTenant() || !userStore.tenantList?.length) {
          // 当前是否有企业
          const res = await getCurrentTenantList();
          // console.log(res, 'getCurrentTenantList')
          await userStore.getTenantList(res.data);
        }
      }
      if (userStore.roles?.[0] === 'CUSTOMER_USER') {
        const userProject = await getUserProject(userStore.id);
        const projectD: any[] = [];
        for (const item of userProject.data) {
          projectD.push(item.id);
        }
        appStore.SetUserProject({ pData: projectD, isSet: true });
      }
      // NProgress.done()
      return true;
    }
    // NProgress.done()
    return { name: 'NotFound' };
  }
  // SET_shouldRefreshSideBar
});
router.afterEach((to, from) => {
  const permissionStore = usePermissionStore();
  const tagsStore = useTagsStore();
  NProgress.done(); // 结束Progress
  if (to.path === '/home' && from.path === '/login') {
    let path = '';
    if (permissionStore.bigScreenR[0]) {
      path = permissionStore.bigScreenR[0].path;
      router.push(path);
    }
  }
  if (to.meta?.title && to.name !== 'home' && to.path !== '404withlayout') {
    tagsStore.ADD_cachedRouters(to);
  }
});
