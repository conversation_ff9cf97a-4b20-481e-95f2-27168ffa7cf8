# 单点登录死循环问题修复总结

## 问题描述

用户中心登录页与login页面相互跳转，出现死循环的问题。

## 问题根因分析

### 1. 重复的路由守卫

系统中存在两个路由守卫文件：

- `src/router/index.ts` 中的路由守卫
- `src/plugins/permission.ts` 中的路由守卫（已删除）

### 2. 白名单配置不一致

- `src/router/index.ts` 中的白名单**不包含** `/login`（已修复）
- `src/plugins/permission.ts` 中的白名单**包含** `/login`

### 3. 死循环流程

1. 用户访问任何需要认证的页面 → 没有token → 跳转到用户中心SSO
2. 用户中心认证成功 → 回调到 `/login?code=xxx`
3. `src/router/index.ts` 的路由守卫执行：发现 `/login` 不在白名单中，且没有token → 再次跳转到用户中心SSO
4. 形成死循环

## 修复方案

### 1. 统一路由守卫

- ✅ 删除重复的 `src/plugins/permission.ts` 文件
- ✅ 在 `src/router/index.ts` 中统一管理路由守卫逻辑

### 2. 修复白名单配置

- ✅ 在 `src/router/index.ts` 中将 `/login` 添加到白名单
- ✅ 确保SSO回调页面可以正常访问

### 3. 优化登录页面体验

- ✅ 改进登录页面UI，显示加载状态和重试按钮
- ✅ 添加SSO登录失败时的错误处理
- ✅ 清理URL中的code参数，避免页面刷新时重复处理
- ✅ 提供重新登录按钮，让用户可以手动重试

### 4. 防循环机制

- ✅ 在用户信息获取失败时，不自动跳转到用户中心，防止循环
- ✅ 使用 `ssoProcessed` 标志防止重复处理同一个code
- ✅ 登录失败时显示友好的错误提示和重试选项

## 修改的文件

1. **src/utils/ssoConfig.ts**（新增）
   - 统一的SSO配置常量管理
   - `buildSsoUrl()` 函数用于构建SSO登录URL
   - `redirectToSSO()` 函数用于跳转到SSO登录页
   - 消除多处硬编码，提高维护性

2. **src/router/index.ts**
   - 将 `/login` 添加到白名单
   - 删除本地SSO配置，改用统一配置
   - 导入并使用 `buildSsoUrl` 函数

3. **src/views/login/index.vue**
   - 优化UI显示逻辑
   - 添加重试登录功能
   - 改进错误处理
   - 清理URL参数
   - 删除本地SSO配置，改用统一的 `redirectToSSO()` 方法
   - 修改 `onMounted` 在没有code时延迟跳转到SSO（避免页面闪烁）

4. **src/store/modules/user.ts**
   - 删除本地SSO配置，改用统一的 `redirectToSSO()` 方法
   - 简化LogOut方法中的SSO跳转逻辑

5. **src/plugins/permission.ts**
   - 删除重复的路由守卫文件

## 测试建议

1. **正常SSO登录流程**
   - 访问需要认证的页面
   - 自动跳转到用户中心
   - 登录成功后回调到应用

2. **SSO登录失败处理**
   - 模拟登录失败场景
   - 验证是否显示重试按钮
   - 验证重试功能是否正常

3. **防循环机制**
   - 验证不会出现无限跳转
   - 验证错误处理是否正确

## 代码改进

### 统一SSO配置管理

- **问题**：SSO配置（BASE、CLIENT_ID、STATE等）在3个文件中重复硬编码
- **解决方案**：创建 `src/utils/ssoConfig.ts` 统一管理
- **优势**：
  - 单一数据源，避免配置不一致
  - 便于维护和修改
  - 提供统一的工具函数
  - 减少代码重复

### 配置对比

**修改前**：

```typescript
// src/router/index.ts
const SSO_BASE = 'http://***********:3002/#/login';
const SSO_CLIENT_ID = 'huachi-water';
const SSO_STATE = 'ejDeIX';

// src/views/login/index.vue
const ssoBase = 'http://***********:3002/#/login';
const clientId = 'huachi-water';
const stateStr = 'ejDeIX';

// src/store/modules/user.ts
const ssoBase = 'http://***********:3002/#/login';
const clientId = 'huachi-water';
const stateStr = 'ejDeIX';
```

**修改后**：

```typescript
// src/utils/ssoConfig.ts
export const SSO_CONFIG = {
  BASE: 'http://***********:3002/#/login',
  CLIENT_ID: 'huachi-water',
  STATE: 'ejDeIX',
  REDIRECT_PATH: '/login',
} as const;

export const redirectToSSO = () => {
  const ssoUrl = buildSsoUrl();
  window.location.href = ssoUrl;
};

// 其他文件只需导入使用
import { redirectToSSO } from '@/utils/ssoConfig';
```

## 预期效果

- ✅ 解决死循环问题
- ✅ 提供更好的用户体验
- ✅ 统一路由守卫逻辑
- ✅ 增强错误处理能力
- ✅ 统一SSO配置管理
- ✅ 提高代码维护性
