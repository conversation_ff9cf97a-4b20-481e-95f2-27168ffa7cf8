// 使用技术：vue-router4
// 基础路由：constantRouterMap
// 异步路由：asyncRouterMap
// modules中对应每个菜单路由数据，同级路由name不可冲突
// tenant-Admin交给后端添加 sys-admin前端写死
// 路由属性：hidden 路由显示/隐藏
// alwaysShow 当children只有一个路由时，父菜单显示/隐藏

// 20220509 lc
// 多级目录修复，根级目录component属性使用Layout,非最子级目录的component属性使用LayoutParentView

import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress'; // Progress 进度条
import { ElMessage } from 'element-plus';
import { constantRouterMap } from './modules';
import {
  useAppStore,
  useBusinessStore,
  usePermissionStore,
  useTagsStore,
  useUserStore,
} from '@/store';
import 'nprogress/nprogress.css'; // Progress 进度条样式
// import { getToken } from '@/api/login' // 验权
import { getUserProject } from '@/api/user';
import { getCurTenant, getCurrentTenantList } from '@/api/tenant';
import { GetDeepestRoutePath, hasPermission, refreshAllRoutes } from '@/utils/RouterHelper';
import { buildSsoUrl } from '@/utils/ssoConfig';

NProgress.configure({ showSpinner: false }); // NProgress Configuration n进程配置
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: constantRouterMap,
});

let routerJumpCounter = 0;
const whiteList = [
  '/login',
  '/resetPassword',
  '/requestResetPwd',
  '/jumping',
  '/jumpingV2',
  '/workorder/new',
]; // 不重定向白名单 /realTimeAlarm , '/eventAlarm/historyAlarm', '/scada', '/phonePage'
// SSO 配置现在统一在 @/utils/ssoConfig 中管理
router.beforeEach(async to => {
  const keys = router.getRoutes();
  NProgress.start();
  const appStore = useAppStore();
  const userStore = useUserStore();
  const businessStore = useBusinessStore();
  const permissionStore = usePermissionStore();
  // 每次跳转前先关闭菜单
  appStore.TOGGLE_menuShow(false);
  const token = userStore.token;
  // 在免登录白名单，直接进入
  if (whiteList.indexOf(to.path) !== -1) {
    NProgress.done();
  } else if (!token) {
    NProgress.done();
    // 没有 token，则重定向到用户中心登录页（携带回调到 /login）
    // 若当前访问的是白名单内的页面，允许访问（上面已处理）
    const ssoUrl = buildSsoUrl('/login');
    window.location.href = ssoUrl;
    // 中断当前路由导航
    return false;
  } else {
    try {
      const { user } = userStore;
      if (!user) {
        // 没有用户信息则拿取用户信息
        await userStore.GetInfo();
        await Promise.all([userStore.GetBtnPerms(), userStore.InitTenantInfo()]);
      }
      // 设置全局项目树
      await businessStore.INIT_projectInfo();
      // if (businessStore.shouldRefreshSideBar) {
      //   await handleAppChange()
      //   return {
      //     path: to.path,
      //     query: to.query,
      //     meta: to.meta
      //   }
      // }
    } catch (error) {
      ElMessage.error('登录信息超时');
      NProgress.done();
      userStore.LogOut();
    }

    if (to.path === '/home') {
      appStore.SET_appid('');
      appStore.SET_appname('');
      await refreshAllRoutes();
      return true;
    }
    const permited = hasPermission(to.meta.roles);

    if (permited === true) {
      const roles = userStore.user?.authority?.split(',') || [];
      if (roles.indexOf('SYS_ADMIN') === -1) {
        if (to.path !== '/404withlayout') {
          if (!permissionStore.addRouters?.length) {
            // 不是管理员且没有从后台获取路由，则重新获取路由
            await refreshAllRoutes();
            // 处理没有获取到路由信息时跳转到404
            const addRouters = permissionStore.addRouters || [];
            // const path = GetDeepestRoutePath(addRouters[0])
            // 处理当原来路由，原来是'/'则跳转到异步路由的第一个最深层级的可访问路由
            const path = to.fullPath === '/' ? GetDeepestRoutePath(addRouters[0]) : to.fullPath;
            //
            if (!addRouters.length) {
              // 重复尝试最多1次
              if (routerJumpCounter >= 1) {
                routerJumpCounter = 0;
              } else {
                // 尝试次数+1并重新跳转一遍
                routerJumpCounter++;
                // return to
                if (path) {
                  return { path };
                }
              }
            } else {
              routerJumpCounter = 0;
              if (path) {
                return { path };
              }
              // return to
            }
          } else {
            routerJumpCounter = 0;
          }
        } else {
          routerJumpCounter = 0;
        }
      } else if (!permissionStore.routers.length) {
        // 是管理员但是没有处理过路由，则处理路由
        await refreshAllRoutes();
        const routers =
          permissionStore.routers?.filter(
            item => item.hidden !== true && item.meta?.hidden !== true,
          ) || [];
        const path = to.fullPath === '/' ? GetDeepestRoutePath(routers[0]) : to.fullPath;
        if (path) {
          return { path };
        }
      }
      // 处理企业列表
      if (roles.indexOf('TENANT_SUPPORT') !== -1 || roles.indexOf('TENANT_PROMOTE') !== -1) {
        if (!getCurTenant() || !userStore.tenantList?.length) {
          // 当前是否有企业
          const res = await getCurrentTenantList();
          // console.log(res, 'getCurrentTenantList')
          await userStore.getTenantList(res.data);
        }
      }
      if (userStore.roles?.[0] === 'CUSTOMER_USER') {
        const userProject = await getUserProject(userStore.id);
        const projectD: any[] = [];
        for (const item of userProject.data) {
          projectD.push(item.id);
        }
        appStore.SetUserProject({ pData: projectD, isSet: true });
      }
      // NProgress.done()
      return true;
    }
    // NProgress.done()
    return { name: 'NotFound' };
  }
  // SET_shouldRefreshSideBar
});
//Tag添加时机
router.afterEach(async (to, from) => {
  NProgress.done(); // 结束Progress
  const permissionStore = usePermissionStore();
  const tagsStore = useTagsStore();
  const appStore = useAppStore();

  if (to.path === '/home' && from.path === '/login') {
    let path = '';
    if (permissionStore.bigScreenR[0]) {
      path = permissionStore.bigScreenR[0].path;
      router.push(path);
    }
  }

  // 根据路由meta中的appid更新顶部菜单高亮状态
  if (to.meta?.appid && to.meta.appid !== appStore.appid) {
    const businessStore = useBusinessStore();
    const targetApp = businessStore.curNavs.find(nav => nav.id === to.meta.appid);
    if (targetApp) {
      appStore.SET_appid(targetApp.id);
      appStore.SET_appname(targetApp.name);
      // 刷新路由以加载对应应用的二级菜单
      await refreshAllRoutes();
    }
  }

  if (to.meta?.title && to.name !== 'home' && to.path !== '404withlayout') {
    tagsStore.ADD_cachedRouters(to);
  }
});

export default router;
