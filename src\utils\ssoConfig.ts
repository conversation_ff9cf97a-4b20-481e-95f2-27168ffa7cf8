/**
 * SSO 单点登录配置
 * 统一管理SSO相关配置，避免多处硬编码
 */

export const SSO_CONFIG = {
  /** 用户中心登录页地址 */
  BASE: 'http://10.0.31.213:3002/#/login',
  /** 客户端ID */
  CLIENT_ID: 'huachi-water',
  /** 状态参数 */
  STATE: 'ejDeIX',
  /** 回调路径 */
  REDIRECT_PATH: '/login'
} as const;

/**
 * 构建SSO登录URL
 * @param redirectPath 回调路径，默认为 /login
 * @returns SSO登录URL
 */
export const buildSsoUrl = (redirectPath = SSO_CONFIG.REDIRECT_PATH): string => {
  const redirectUri = encodeURIComponent(window.location.origin + redirectPath);
  return `${SSO_CONFIG.BASE}?client_id=${SSO_CONFIG.CLIENT_ID}&redirect_uri=${redirectUri}&response_type=code&state=${SSO_CONFIG.STATE}`;
};

/**
 * 跳转到SSO登录页
 * @param redirectPath 回调路径，默认为 /login
 */
export const redirectToSSO = (redirectPath = SSO_CONFIG.REDIRECT_PATH): void => {
  const ssoUrl = buildSsoUrl(redirectPath);
  window.location.href = ssoUrl;
};
