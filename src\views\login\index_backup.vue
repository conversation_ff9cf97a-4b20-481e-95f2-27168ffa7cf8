<template>
  <div class="login-wrapper">
    <video ref="videoRef" class="video-background" autoplay loop muted>
      <source src="/华池.mp4" type="video/mp4" />
    </video>

    <!-- sound toggle 按钮 -->
    <div class="sound-toggle" title="切换背景声音" @click="toggleSound">
      <template v-if="state.soundOn">
        <!-- 声音开启 -->
        <svg viewBox="0 0 24 24" aria-hidden="true" focusable="false">
          <path d="M3 10v4h4l5 5V5L7 10H3z"></path>
          <path d="M16.5 12a4.5 4.5 0 0 0-1.5-3.5v7A4.5 4.5 0 0 0 16.5 12z"></path>
        </svg>
      </template>
      <template v-else>
        <!-- 静音 / 带斜线 -->
        <svg viewBox="0 0 24 24" aria-hidden="true" focusable="false">
          <path d="M3 10v4h4l5 5V5L7 10H3z"></path>
          <path
            d="M19 5l-14 14"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            fill="none"
          ></path>
        </svg>
      </template>
    </div>

    <div class="login-box">
      <div class="login-box__header">
        <img class="logo" src="/logo.png" alt="" />
        <span class="title">{{ title }}</span>
      </div>
      <el-form
        ref="refForm"
        class="login-box__main"
        auto-complete="on"
        :model="state.loginForm"
        :size="'large'"
        label-position="top"
        @keyup.enter="login"
      >
        <el-form-item :prop="'username'" :rules="[{ required: true, message: '请输入用户名' }]">
          <el-input
            v-model="state.loginForm.username"
            type="text"
            :autocomplete="'on'"
            placeholder="请输入用户名"
          >
            <template #prefix>
              <Icon icon="ep:user"></Icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :prop="'password'" :rules="[{ required: true, message: '请输入密码' }]">
          <el-input
            v-model="state.loginForm.password"
            type="password"
            :autocomplete="'on'"
            placeholder="请输入密码"
            :show-password="true"
          >
            <template #prefix>
              <Icon icon="ep:lock"></Icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="login-box__footer">
        <el-button
          style="width: 100%"
          type="primary"
          size="large"
          :loading="state.loading"
          @click="login"
        >
          登录
        </el-button>
      </div>
    </div>
    <div v-if="loginConfig.SHOWFOOTER">
      <div v-if="loginConfig.SHOWPLUGINS" class="plugins">
        <div class="plugin-wrapp">
          <div v-for="(item, i) in state.plugins" :key="i" class="plugin-item">
            <a :href="item.url" target="_blank">
              <img class="plugin-logo" :class="item.name" :src="getLoginImage(item.img)" alt="" />
            </a>
          </div>
        </div>
      </div>

      <div v-if="loginConfig.SHOWQRCODE" class="qrcode">
        <template v-for="(item, i) in state.qrcodes" :key="i">
          <div v-if="item.url" class="qrcode-item">
            <div class="qrcode-title">
              {{ item.name }}
            </div>
            <QrcodeVue :value="item.url" :size="80" level="H" />
          </div>
        </template>
      </div>
    </div>
    <div v-if="loginConfig.SHOWCOPYRIGHT" class="login-tips">
      {{ loginConfig.COPYRIGHT }}
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import QrcodeVue from 'qrcode.vue';
import { IElForm } from '@/common/types/element-plus';
import { SLMessage } from '@/utils/Message';
import { getKey, scadaLogin } from '@/api/login';
import AES from '@/utils/AES';
import { useBusinessStore, useUserStore } from '@/store';
// import { getTenantInfo } from '@/api/tenant'

const videoRef = ref<HTMLVideoElement | null>(null);
const refForm = ref<IElForm>();
const loginConfig = window.SITE_CONFIG.LOGIN_CONFIG;
const getLoginImage = (name: string) => {
  return new URL(`../../assets/images/login/${name}`, import.meta.url).href;
};
const state = reactive<{
  loading: boolean;
  loginForm: {
    username: string;
    password: string;
  };
  qrcodes: { name: string; url: string }[];
  plugins: { name: string; url: string; img: string }[];
  soundOn: boolean;
}>({
  loading: false,
  loginForm: {
    username: '',
    password: '',
  },
  qrcodes: [
    { name: 'App', url: '' },
    { name: '公众号', url: window.SITE_CONFIG.LOGIN_CONFIG.WXPUBLICACCOUNTURL },
  ],
  plugins: [
    {
      name: 'hik',
      img: 'hik.png',
      url: 'https://pic.hik-cloud.com/chain/download/cloudViewSetup.exe',
    },
    {
      name: 'chrome',
      img: 'chrome.png',
      url: 'https://www.google.com/intl/zh-CN/chrome/thank-you.html?statcb=1&installdataindex=empty&defaultbrowser=0',
    },
    {
      name: 'lodop',
      img: 'lodop.png',
      url: 'https://www.lodop.net/download.html',
    },
  ],
  soundOn: false,
});
const router = useRouter();
const routeTitle = router.currentRoute.value.query?.title;
const title =
  routeTitle ||
  (window.SITE_CONFIG.TITLE || '') + (window.SITE_CONFIG.SUBTITLE || '') ||
  '智慧水务建设平台';

// 切换声音
const toggleSound = () => {
  state.soundOn = !state.soundOn;
  if (videoRef.value) {
    videoRef.value.muted = !state.soundOn;
    // 可选：调整音量
    if (state.soundOn) {
      videoRef.value.volume = 0.6;
    }
  }
};

const login = async () => {
  try {
    const flag = await refForm.value?.validate();
    if (flag === false) {
      return;
    }
    state.loading = true;
    const loginForm = { ...state.loginForm };

    const res = await getKey();
    loginForm.username = AES.encrypt(loginForm.username, res.data.key);
    loginForm.password = AES.encrypt(loginForm.password, res.data.key);
    await useUserStore().Login(loginForm);

    const roles = useUserStore().user?.authority?.split(',') || [];
    router
      .push({
        path: useBusinessStore().usePortal ? (roles[0] === 'SYS_ADMIN' ? '/' : '/app') : '/home',
      })
      .finally(() => {
        state.loading = false;
      });
    // 授权组态
    scadaLogin({
      strategy: 'local',
      username: loginForm.username,
      password: loginForm.password,
    })
      .then(res => {
        useUserStore().ToggleScadaToken(res.data.accessToken);
      })
      .catch(() => {
        console.log('auth scada faild!');
      });
    localStorage.setItem(
      'ysinfo',
      JSON.stringify({
        u: loginForm.username,
        p: loginForm.password,
      }),
    );
  } catch (error: any) {
    SLMessage.error(error.message || '登录失败');
    state.loading = false;
  }
};
const setAppUrl = () => {
  state.qrcodes[0].url = window.SITE_CONFIG.appDownloadUrl; //'http://***********:8999/group1/M00/00/03/wKgA7GWOiQmAQrcPBbB2Az4FKCE867.apk';
};
onMounted(() => {
  setAppUrl();
});
</script>
<style lang="scss" scoped>
.qrcode {
  display: flex;
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 10;

  .qrcode-item {
    display: inline-flex;
    margin-right: 20px;
    flex-direction: column;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 10px;
    border-radius: 8px;
    backdrop-filter: blur(1px);

    .qrcode-title {
      margin-bottom: 10px;
      color: #ffffff;
      font-size: 16px;
      text-align: center;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
  }
}

/* 新增声音开关样式 */
.sound-toggle {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 12;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);

  &:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.12);
  }

  svg {
    width: 20px;
    height: 20px;
    fill: currentColor; /* 确保图标跟随 color */
    stroke: currentColor;
  }
}

.plugins {
  position: absolute;
  left: 20px;
  bottom: 20px;
  display: flex;
  flex-direction: column;
  z-index: 10;
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(1px);

  .plugins-title {
    font-size: 14px;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .plugin-item {
    font-size: 14px;
    line-height: 2;
    margin-bottom: 5px;

    .plugin-logo {
      &.chrome {
        height: 18px;
        margin-left: 8px;
        margin-bottom: 8px;
        filter: brightness(1.2);
      }

      &.lodop {
        height: 16px;
        margin-left: 10px;
        filter: brightness(1.2);
      }

      &.hik {
        height: 30px;
        filter: brightness(1.2);
      }
    }
  }
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.login-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
  font-family: 'PingFang SC';
  font-style: normal;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
    z-index: 2;
  }
}

.login-box {
  position: absolute;
  width: 420px;
  left: 58%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 60px 40px;
  z-index: 10;

  .login-box__header {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    margin-bottom: 40px;

    .logo {
      margin-right: 16px;
      height: 32px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    .title {
      color: #ffffff;
      font-weight: 600;
      font-size: 24px;
      line-height: 32px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
  }

  .login-box__main {
    width: 100%;
    margin-bottom: 30px;

    .el-form-item {
      margin-bottom: 25px;
    }
  }

  .login-box__footer {
    width: 100%;

    :deep(.el-button) {
      background: linear-gradient(135deg, #0080ff 0%, #0062c3 100%);
      border: none;
      height: 46px;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 1px;
      box-shadow: 0 4px 12px rgba(0, 128, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 18px rgba(0, 128, 255, 0.4);
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(0, 128, 255, 0.3);
      }
    }
  }
}

.login-tips {
  position: absolute;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  z-index: 10;
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
  text-align: center;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
}
</style>
<style lang="scss">
.login-box__main {
  .el-input {
    --el-border-color: rgba(255, 255, 255, 0.3);
    --el-input-bg-color: rgba(255, 255, 255, 0.1);

    .el-input__wrapper {
      background-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
      border-radius: 8px;

      &:hover,
      &:focus {
        box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.4);
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #0080ff;
      }
    }

    .el-input__inner {
      color: #ffffff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .el-input__prefix {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  input:-webkit-autofill,
  textarea:-webkit-autofill,
  select:-webkit-autofill {
    background-color: transparent !important;
    background-image: none;
    transition: background-color 50000s ease-in-out 0s;
    -webkit-text-fill-color: #ffffff !important;
  }

  input:-internal-autofill-previewed,
  input:-internal-autofill-selected {
    color: #ffffff !important;
    transition: background-color 50000s ease-in-out 0s !important;
  }

  .el-form-item__label {
    color: rgba(255, 255, 255, 0.9);
  }
}

.el-form-item__error {
  color: #ff6b6b;
}
</style>
